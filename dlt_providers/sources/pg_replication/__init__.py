"""Replicates PostgreSQL tables in batch using logical decoding, with the initial snapshot performed via ConnectorX."""

from typing import Dict, List, Optional, Sequence, Union

import dlt
from dlt.common.schema.typing import TTableSchemaColumns
from dlt.extract import DltResource
from dlt.sources.credentials import ConnectionStringCredentials

from .helpers import (
    create_replication_resource,
    init_replication,
)


@dlt.source
def pg_replication(
    slot_name: str = dlt.config.value,
    pub_name: str = dlt.config.value,
    schema_name: str = dlt.config.value,
    table_names: Optional[Union[str, Sequence[str]]] = dlt.config.value,
    credentials: ConnectionStringCredentials = dlt.secrets.value,
    publish: str = "insert, update, delete",
    include_columns: Optional[Dict[str, Sequence[str]]] = None,
    columns: Optional[Dict[str, TTableSchemaColumns]] = None,
    target_batch_size: int = 1000,
    flush_slot: bool = True,
    initial_snapshot: bool = True,
    include_tables: Optional[Union[str, Sequence[str]]] = None,
    exclude_tables: Optional[Union[str, Sequence[str]]] = None,
    reset: bool = False,
) -> List[DltResource]:
    """PostgreSQL replication source with initial snapshot support using ConnectorX."""
    resources = []

    # Initialize replication (creates slot/publication if needed)
    snapshot_resources = init_replication(
        slot_name=slot_name,
        pub_name=pub_name,
        schema_name=schema_name,
        table_names=table_names,
        credentials=credentials,
        publish=publish,
        initial_snapshot=initial_snapshot,
        include_columns=include_columns,
        columns=columns,
        reset=reset,
        include_tables=include_tables,
        exclude_tables=exclude_tables,
    )

    # Add snapshot resources if created
    if snapshot_resources is not None:
        if isinstance(snapshot_resources, list):
            resources.extend(snapshot_resources)
        else:
            resources.append(snapshot_resources)

    # Add replication resource
    replication_res = create_replication_resource(
        slot_name=slot_name,
        pub_name=pub_name,
        credentials=credentials,
        include_columns=include_columns,
        columns=columns,
        target_batch_size=target_batch_size,
        flush_slot=flush_slot,
    )
    resources.append(replication_res)

    return resources


# Export main function
__all__ = [
    "pg_replication",
]
