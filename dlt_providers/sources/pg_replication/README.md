# Postgres replication
[Postgres](https://www.postgresql.org/) is one of the most popular relational database management systems. This verified source uses Postgres' replication functionality to efficiently process changes in tables (a process often referred to as _Change Data Capture_ or CDC). It uses [logical decoding](https://www.postgresql.org/docs/current/logicaldecoding.html) and the standard built-in `pgoutput` [output plugin](https://www.postgresql.org/docs/current/logicaldecoding-output-plugin.html).

## Key Features

- **ConnectorX Integration**: Uses ConnectorX for efficient initial snapshots without creating any artifacts in the source database
- **Automatic Type Handling**: Automatically handles unsupported PostgreSQL types (geometry, geography, etc.) by casting to text
- **Simplified Publication Logic**: Creates database-level publications if not exist, uses existing ones if available
- **Table Filtering**: Support for include/exclude table filtering using information_schema discovery
- **Read-Only Source**: Requires only read-only + replication permissions, no write access needed
- **Table Validation**: Validates that required tables are accessible through the publication
- **Production Ready**: Designed for production use with proper error handling and batching

Resources that can be loaded using this verified source are:

| Name                 | Description                                     |
|----------------------|-------------------------------------------------|
| pg_replication       | Complete source with snapshot + replication    |
| replication_resource | Load published messages from a replication slot |

## Initialize the pipeline

```bash
dlt init pg_replication duckdb
```

This uses `duckdb` as destination, but you can choose any of the supported [destinations](https://dlthub.com/docs/dlt-ecosystem/destinations/).

## Set up user

The Postgres user needs to have the `LOGIN` and `REPLICATION` attributes assigned:

```sql
CREATE ROLE replication_user WITH LOGIN REPLICATION;
```

**Note**: With the new ConnectorX-based snapshots, the user no longer needs `CREATE` privilege on the database. Only read access is required:

```sql
-- Grant read access to schema and tables
GRANT USAGE ON SCHEMA public TO replication_user;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO replication_user;
GRANT SELECT ON ALL SEQUENCES IN SCHEMA public TO replication_user;

-- Grant access to future tables
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO replication_user;
```

### Set up RDS
1. You must enable replication for RDS Postgres instance via **Parameter Group**: https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/USER_PostgreSQL.Replication.ReadReplicas.html
2. `WITH LOGIN REPLICATION;` does not work on RDS, instead do:
```sql
GRANT rds_replication TO replication_user;
```
3. Do not fallback to non SSL connection by setting connection parameters:
```toml
sources.pg_replication.credentials="postgresql://loader:<EMAIL>:5432/dlt_data?sslmode=require&connect_timeout=300"
```


## Add credentials
1. Open `.dlt/secrets.toml`.
2. Enter your Postgres credentials:

    ```toml
    [sources.pg_replication]
    credentials="postgresql://replication_user:<<password>>@localhost:5432/dlt_data"
    ```
3. Enter credentials for your chosen destination as per the [docs](https://dlthub.com/docs/dlt-ecosystem/destinations/).

## Run the pipeline

1. Install the necessary dependencies by running the following command:

   ```bash
   pip install -r requirements.txt
   pip install connectorx  # Required for snapshot functionality
   ```

2. Now the pipeline can be run by using the command:

   ```bash
   python pg_replication_pipeline.py
   ```

3. To make sure that everything is loaded as expected, use the command:

   ```bash
   dlt pipeline pg_replication_pipeline show
   ```

## Usage Examples

### Basic Usage with Initial Snapshot

```python
import dlt
from dlt_providers.sources.pg_replication import pg_replication

# Create source with initial snapshot
source = pg_replication(
    slot_name="my_replication_slot",
    pub_name="my_publication",
    schema_name="public",
    initial_snapshot=True,  # Uses ConnectorX for efficient snapshots
)

pipeline = dlt.pipeline(
    pipeline_name="postgres_replication",
    destination="duckdb",
    dataset_name="replicated_data"
)

load_info = pipeline.run(source)
print(load_info)
```

### Table Filtering (Works with Any Publication)

```python
# Works with existing publication or creates new database-level publication
source = pg_replication(
    slot_name="filtered_slot",
    pub_name="my_publication",  # Creates database-level publication if not exists
    schema_name="public",
    include_tables=["users", "orders", "products"],  # Only these tables
    exclude_tables=["temp_table"],  # Exclude this table
    reset=False,  # Don't recreate existing slot/publication
)
```

### Column Filtering and Hints

```python
source = pg_replication(
    slot_name="column_filtered_slot",
    pub_name="column_filtered_pub",
    schema_name="public",
    table_names=["users", "orders"],
    # Include only specific columns per table
    include_columns={
        "users": ["id", "name", "email", "created_at"],
        "orders": ["id", "user_id", "total", "status", "created_at"]
    },
    # Apply column hints
    columns={
        "users": {
            "email": {"data_type": "text"},
            "created_at": {"data_type": "timestamp"}
        },
        "orders": {
            "total": {"data_type": "decimal", "precision": 10, "scale": 2}
        }
    }
)
```

## Handling Unsupported Data Types

The source uses a **protocol switching approach** to handle PostgreSQL data types that are not supported by ConnectorX:

### **Protocol Switching Strategy**
1. **Try different protocols**: ConnectorX supports `binary`, `cursor`, and `csv` protocols
2. **Protocol-specific handling**: Different protocols handle unsupported types differently
3. **Selective casting**: Only cast problematic columns when needed, not all columns
4. **Graceful degradation**: Falls back through multiple approaches before failing

### **Supported Types (No Casting Needed)**
- **Numeric**: `bool`, `int2`, `int4`, `int8`, `float4`, `float8`, `numeric`
- **Text**: `text`, `varchar`, `char`, `bytea`
- **Date/Time**: `date`, `time`, `timestamp`, `timestamptz`
- **Complex**: `uuid`, `json`, `jsonb` (as object)
- **Arrays**: `int2[]`, `int4[]`, `int8[]`, `float4[]`, `float8[]`, `numeric[]`

### **Automatically Cast to Text**
- **Geometry types**: `geometry`, `geography`, `point`, `polygon`, etc.
- **Custom types**: Any user-defined types, enums, etc.
- **Advanced types**: `xml`, `tsvector`, `ltree`, `inet`, etc.

### **How It Works**

```python
# Example: Table with geometry columns
source = pg_replication(
    slot_name="geometry_slot",
    pub_name="geometry_pub",
    schema_name="inventory",  # Debezium example with geometry
    initial_snapshot=True,
)

# The source automatically tries multiple approaches:
# 1. Binary protocol with original query (fastest)
# 2. Cursor protocol with original query
# 3. CSV protocol with original query
# 4. Binary protocol with selective casting (if needed)
# 5. Cursor protocol with selective casting
# 6. CSV protocol with selective casting
```

### **Protocol-Based Error Recovery**

The source provides multiple levels of fallback without affecting good columns:

```
┌─ Try: Binary protocol (fastest, most type-strict)
├─ Try: Cursor protocol (more flexible)
├─ Try: CSV protocol (most flexible, text-based)
│
├─ If all fail: Try with selective column casting
│  ├─ Binary + selective casting
│  ├─ Cursor + selective casting
│  └─ CSV + selective casting
│
└─ If still fails: Clear error message with suggestions
```

### **Benefits of Protocol Switching**

- **No unnecessary casting**: Good columns remain in their native types
- **Better performance**: Uses fastest protocol that works
- **Maximum compatibility**: CSV protocol handles almost any type
- **Preserves data quality**: Only problematic columns are affected

### Custom Type Handling

If you encounter unsupported types not covered by the automatic casting, you can:

1. **Exclude problematic tables**: Use `exclude_tables` parameter
2. **Filter columns**: Use `include_columns` to exclude problematic columns
3. **Manual casting**: Create a view with manual casting and replicate the view instead

```python
# Exclude tables with problematic types
source = pg_replication(
    slot_name="filtered_slot",
    pub_name="filtered_pub",
    schema_name="public",
    exclude_tables=["table_with_custom_types"],
)

# Or filter specific columns
source = pg_replication(
    slot_name="column_filtered_slot",
    pub_name="column_filtered_pub",
    schema_name="public",
    include_columns={
        "my_table": ["id", "name", "created_at"]  # Exclude geometry column
    }
)
```