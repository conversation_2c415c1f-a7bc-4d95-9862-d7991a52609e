"""Example usage of the improved PostgreSQL replication source."""

import dlt
from dlt.sources.credentials import ConnectionStringCredentials

# Import the new pg_replication source
from . import pg_replication


def example_basic_replication():
    """Basic example with initial snapshot and replication."""
    
    # Configure credentials
    credentials = ConnectionStringCredentials(
        "postgresql://user:password@localhost:5432/mydb"
    )
    
    # Create the source with initial snapshot
    source = pg_replication(
        slot_name="my_replication_slot",
        pub_name="my_publication", 
        schema_name="public",
        credentials=credentials,
        initial_snapshot=True,  # Creates initial snapshot using ConnectorX
        flush_slot=True,
    )
    
    # Create pipeline and run
    pipeline = dlt.pipeline(
        pipeline_name="postgres_replication",
        destination="duckdb",
        dataset_name="replicated_data"
    )
    
    load_info = pipeline.run(source)
    print(load_info)


def example_with_table_filtering():
    """Example with table filtering - works with any publication."""

    credentials = ConnectionStringCredentials(
        "postgresql://user:password@localhost:5432/mydb"
    )

    # Works with existing publication or creates new database-level publication
    source = pg_replication(
        slot_name="filtered_slot",
        pub_name="my_publication",  # Creates database-level publication if not exists
        schema_name="public",
        credentials=credentials,
        initial_snapshot=True,
        include_tables=["users", "orders", "products"],  # Only these tables
        exclude_tables=["temp_table"],  # Exclude this table
        reset=False,  # Don't recreate existing slot/publication
    )

    pipeline = dlt.pipeline(
        pipeline_name="filtered_replication",
        destination="bigquery",
        dataset_name="filtered_data"
    )

    load_info = pipeline.run(source)
    print(load_info)


def example_column_filtering():
    """Example with column filtering and hints."""
    
    credentials = ConnectionStringCredentials(
        "postgresql://user:password@localhost:5432/mydb"
    )
    
    source = pg_replication(
        slot_name="column_filtered_slot",
        pub_name="column_filtered_pub",
        schema_name="public",
        table_names=["users", "orders"],
        credentials=credentials,
        initial_snapshot=True,
        # Include only specific columns per table
        include_columns={
            "users": ["id", "name", "email", "created_at"],
            "orders": ["id", "user_id", "total", "status", "created_at"]
        },
        # Apply column hints
        columns={
            "users": {
                "email": {"data_type": "text"},
                "created_at": {"data_type": "timestamp"}
            },
            "orders": {
                "total": {"data_type": "decimal", "precision": 10, "scale": 2}
            }
        }
    )
    
    pipeline = dlt.pipeline(
        pipeline_name="column_filtered_replication", 
        destination="snowflake",
        dataset_name="clean_data"
    )
    
    load_info = pipeline.run(source)
    print(load_info)


def example_production_ready():
    """Production-ready example with all best practices."""
    
    # Use secrets for credentials
    credentials = dlt.secrets.value
    
    source = pg_replication(
        slot_name=dlt.config.value,  # From config
        pub_name=dlt.config.value,   # From config
        schema_name=dlt.config.value, # From config
        credentials=credentials,
        initial_snapshot=True,
        target_batch_size=5000,  # Larger batches for production
        flush_slot=True,  # Always flush in production
        publish="insert, update, delete",  # All operations
        # Table filtering from config
        include_tables=dlt.config.value,
        exclude_tables=dlt.config.value,
    )
    
    pipeline = dlt.pipeline(
        pipeline_name="production_replication",
        destination=dlt.config.value,  # From config
        dataset_name=dlt.config.value,  # From config
    )
    
    # Run with error handling
    try:
        load_info = pipeline.run(source)
        print(f"Successfully loaded {load_info}")
        
        # Check for any load errors
        if load_info.has_failed_jobs:
            print("Some jobs failed:")
            for job in load_info.failed_jobs:
                print(f"  - {job.job_file_info.file_name}: {job.failed_message}")
                
    except Exception as e:
        print(f"Pipeline failed: {e}")
        raise


if __name__ == "__main__":
    # Run basic example
    example_basic_replication()
