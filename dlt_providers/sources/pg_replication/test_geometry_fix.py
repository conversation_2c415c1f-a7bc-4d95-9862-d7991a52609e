"""Test script to verify geometry type handling in PostgreSQL replication source."""

import dlt
from dlt.sources.credentials import ConnectionStringCredentials
from dlt_providers.sources.pg_replication import pg_replication


def test_geometry_handling():
    """Test that geometry columns are properly handled using protocol switching."""

    # Example credentials - replace with your actual database
    credentials = ConnectionStringCredentials(
        "postgresql://user:password@localhost:5432/inventory"
    )

    try:
        # Create the source with initial snapshot
        source = pg_replication(
            slot_name="test_geometry_slot",
            pub_name="test_geometry_pub",
            schema_name="inventory",  # Debezium example schema
            credentials=credentials,
            initial_snapshot=True,
            target_batch_size=100,
            flush_slot=True,
            reset=False,  # Don't reset existing slot/publication
        )

        # Create pipeline
        pipeline = dlt.pipeline(
            pipeline_name="test_geometry_replication",
            destination="duckdb",
            dataset_name="test_geometry_data"
        )

        # Run the pipeline
        print("Running pipeline with protocol switching for geometry types...")
        load_info = pipeline.run(source)
        print(f"Pipeline completed successfully: {load_info}")

        # Check what was loaded
        row_counts = pipeline.last_trace.last_normalize_info.row_counts
        print(f"Row counts: {row_counts}")

        return True

    except Exception as e:
        print(f"Error: {e}")
        if "not implemented: geometry" in str(e):
            print("❌ Geometry type is still not handled properly")
            return False
        else:
            print(f"❌ Different error occurred: {e}")
            return False


def test_query_builder():
    """Test the query builder function directly."""
    from dlt_providers.sources.pg_replication.helpers import (
        build_connectorx_compatible_query,
        build_fallback_query,
        get_table_columns_with_types,
        get_connectorx_supported_types,
        _get_conn
    )

    credentials = ConnectionStringCredentials(
        "postgresql://user:password@localhost:5432/inventory"
    )

    try:
        with _get_conn(credentials) as conn:
            with conn.cursor() as cur:
                # Test supported types detection
                supported_types = get_connectorx_supported_types()
                print(f"ConnectorX supported types: {len(supported_types)} types")

                # Test getting column types for a table with geometry
                if True:  # Test with geom table
                    try:
                        columns = get_table_columns_with_types("geom", "inventory", cur)
                        print(f"Columns in geom table: {columns}")

                        # Test building compatible query
                        query = build_connectorx_compatible_query("geom", "inventory", cur)
                        print(f"Generated smart query: {query}")

                        # Test fallback query
                        fallback_query = build_fallback_query("geom", "inventory", cur)
                        print(f"Generated fallback query: {fallback_query}")

                    except Exception as e:
                        print(f"Geom table test failed: {e}")

                # Test with customers table (should work normally)
                columns = get_table_columns_with_types("customers", "inventory", cur)
                print(f"Columns in customers table: {columns}")

                query = build_connectorx_compatible_query("customers", "inventory", cur)
                print(f"Generated query for customers: {query}")

                return True

    except Exception as e:
        print(f"Query builder test failed: {e}")
        return False


if __name__ == "__main__":
    print("Testing PostgreSQL geometry type handling...")
    
    # Test query builder first
    print("\n1. Testing query builder...")
    if test_query_builder():
        print("✅ Query builder test passed")
    else:
        print("❌ Query builder test failed")
    
    # Test full pipeline
    print("\n2. Testing full pipeline...")
    if test_geometry_handling():
        print("✅ Geometry handling test passed")
    else:
        print("❌ Geometry handling test failed")
