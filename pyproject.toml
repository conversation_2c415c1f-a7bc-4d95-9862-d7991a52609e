[project]
name = "dlt-providers"
dynamic = ["version"]
description = "Providers for data load tool (dlt)"
readme = "README.md"
requires-python = ">=3.10"
dependencies = ["dlt~=1.12.1"]

[project.optional-dependencies]
pg_replication = ["psycopg2-binary==2.9.10", "connectorx==0.4.3"]
mysql_replication = ["mysql-replication==1.0.9", "connectorx==0.4.3"]

[build-system]
requires = ["hatchling", "uv-dynamic-versioning"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["dlt_providers"]

[tool.hatch.version]
source = "uv-dynamic-versioning"

[tool.uv-dynamic-versioning]
pattern = "default-unprefixed"
